// 测试加密功能
import { generateAesKey, encryptBase64, encryptWithAes } from './utils/crypto.js'
import { encrypt } from './utils/jsencrypt.js'

// 测试数据
const testData = {
  clientId: '428a8310cd442757ae699df5d894f051',
  grantType: 'xcx',
  tenantId: '779984',
  xcxCode: 'test_code_123'
}

console.log('=== 加密测试 ===')
console.log('原始数据:', testData)

try {
  // 1. 生成AES密钥
  const aesKey = generateAesKey()
  console.log('AES密钥生成成功')

  // 2. AES加密数据
  const dataStr = JSON.stringify(testData)
  const encryptedData = encryptWithAes(dataStr, aesKey)
  console.log('AES加密成功:', encryptedData.substring(0, 50) + '...')

  // 3. Base64编码AES密钥
  const base64Key = encryptBase64(aesKey)
  console.log('Base64编码成功:', base64Key.substring(0, 50) + '...')

  // 4. RSA加密Base64密钥
  const rsaEncryptedKey = encrypt(base64Key)
  console.log('RSA加密成功:', rsaEncryptedKey ? 'YES' : 'NO')

  console.log('=== 加密测试完成 ===')
} catch (error) {
  console.error('加密测试失败:', error)
}
