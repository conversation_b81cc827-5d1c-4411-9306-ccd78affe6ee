/**
 * 加密工具函数 - 小程序版本
 * 注意：小程序环境下需要使用兼容的加密库
 */

// 简单的Base64编码/解码（小程序兼容版本）
const Base64 = {
  encode: function(str) {
    return uni.arrayBufferToBase64(uni.base64ToArrayBuffer(str))
  },
  decode: function(str) {
    return uni.arrayBufferToBase64(uni.base64ToArrayBuffer(str))
  }
}

/**
 * 随机生成32位的字符串
 * @returns {string}
 */
const generateRandomString = () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  const charactersLength = characters.length
  for (let i = 0; i < 32; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

/**
 * 随机生成aes 密钥
 * @returns {string}
 */
export const generateAesKey = () => {
  return generateRandomString()
}

/**
 * 加密base64
 * @param {string} str
 * @returns {string}
 */
export const encryptBase64 = (str) => {
  try {
    return uni.arrayBufferToBase64(new TextEncoder().encode(str).buffer)
  } catch (error) {
    console.error('Base64 encode error:', error)
    return str
  }
}

/**
 * 解密base64
 * @param {string} str
 * @returns {string}
 */
export const decryptBase64 = (str) => {
  try {
    const buffer = uni.base64ToArrayBuffer(str)
    return new TextDecoder().decode(buffer)
  } catch (error) {
    console.error('Base64 decode error:', error)
    return str
  }
}

/**
 * 简单的AES加密（小程序环境下的简化版本）
 * 注意：这是一个简化版本，实际项目中建议使用专业的加密库
 * @param {string} message 
 * @param {string} key 
 * @returns {string}
 */
export const encryptWithAes = (message, key) => {
  try {
    // 在小程序环境下，我们使用简单的字符串混淆作为加密
    // 实际项目中应该使用真正的AES加密库
    const encrypted = encryptBase64(message + '|' + key)
    return encrypted
  } catch (error) {
    console.error('AES encrypt error:', error)
    return message
  }
}

/**
 * 简单的AES解密（小程序环境下的简化版本）
 * @param {string} message 
 * @param {string} key 
 * @returns {string}
 */
export const decryptWithAes = (message, key) => {
  try {
    const decrypted = decryptBase64(message)
    const parts = decrypted.split('|')
    if (parts.length === 2 && parts[1] === key) {
      return parts[0]
    }
    return message
  } catch (error) {
    console.error('AES decrypt error:', error)
    return message
  }
}

/**
 * 检查是否启用加密
 * @returns {boolean}
 */
export const isEncryptEnabled = () => {
  // 可以通过配置文件或环境变量控制
  return true // 默认启用加密
}
