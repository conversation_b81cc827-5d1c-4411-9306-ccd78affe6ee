import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { login as loginApi } from '@/api/login'

const _loginRes = uni.getStorageSync('loginRes') ? uni.getStorageSync('loginRes') : {}
export const useUserStore = defineStore('user', () => {
  const loginRes = ref(_loginRes)
  const clientId = ref('e5cd7e4891bf95d1d19206ce24a7b32e')
  const grantType = ref('xcx')
  const tenantId = ref('000000')
  // const agentId=ref('1956257023227404289')  //  助手id

  const accessToken = computed(() => {
    return loginRes.value.access_token
  })

  function saveLoginRes(obj) {
    loginRes.value = obj
    uni.setStorageSync('loginRes', obj)
  }

  function clearStorage() {
    loginRes.value = {}
    uni.removeStorageSync('loginRes')
  }

  /**
   * 登录方法
   * @param {Object} userInfo 登录信息
   * @returns {Promise}
   */
  async function login(userInfo) {
    try {
      const res = await loginApi(userInfo)
      if (res.code === 200) {
        const data = res.data
        saveLoginRes(data)
        return Promise.resolve(data)
      } else {
        uni.showToast({
          title: res.msg || '登录失败',
          icon: 'none',
        })
        return Promise.reject(res)
      }
    } catch (error) {
      uni.showToast({
        title: '登录失败',
        icon: 'none',
      })
      return Promise.reject(error)
    }
  }
  // function saveAgentId(id=''){
  // 	agentId.value=id
  // }

  return {
    loginRes,
    clientId,
    grantType,
    tenantId,
    // agentId,
    accessToken,
    saveLoginRes,
    clearStorage,
    login,
  }
})
