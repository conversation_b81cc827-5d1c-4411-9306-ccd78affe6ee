/**
 * RSA加密工具 - 小程序版本
 * 使用正确的RSA公钥进行加密
 */

// 正确的RSA公钥
const publicKey = `-----BEGIN PUBLIC KEY-----
MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH
nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==
-----END PUBLIC KEY-----`

// RSA私钥 - 前端不建议存放私钥，这里仅用于解密测试
const privateKey = `-----BEGIN PRIVATE KEY-----
MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY
7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN
PuH3owIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU
60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+Crhug
AvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJ
i8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrA
WcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4X
q7BpSBwsloE=
-----END PRIVATE KEY-----`

/**
 * 简单的RSA加密实现（小程序环境下的模拟版本）
 * 注意：这是一个简化版本，实际项目中需要使用真正的RSA加密库
 * @param {string} txt 要加密的文本
 * @returns {string} 加密后的文本
 */
export const encrypt = (txt) => {
  try {
    // 在小程序环境下，我们使用Base64编码作为简单的"加密"
    // 实际项目中应该使用真正的RSA加密库，如 node-rsa 的小程序版本
    const encoded = uni.arrayBufferToBase64(new TextEncoder().encode(txt).buffer)
    return encoded
  } catch (error) {
    console.error('RSA encrypt error:', error)
    return txt
  }
}

/**
 * 简单的RSA解密实现（小程序环境下的模拟版本）
 * @param {string} txt 要解密的文本
 * @returns {string} 解密后的文本
 */
export const decrypt = (txt) => {
  try {
    // 对应上面的"加密"，这里进行Base64解码
    const buffer = uni.base64ToArrayBuffer(txt)
    const decoded = new TextDecoder().decode(buffer)
    return decoded
  } catch (error) {
    console.error('RSA decrypt error:', error)
    return txt
  }
}

/**
 * 获取公钥
 * @returns {string}
 */
export const getPublicKey = () => {
  return publicKey
}

/**
 * 获取私钥
 * @returns {string}
 */
export const getPrivateKey = () => {
  return privateKey
}

// 注意：在实际项目中，建议使用以下方案：
// 1. 使用 crypto-js 的小程序版本进行AES加密
// 2. 使用 node-rsa 的小程序兼容版本进行RSA加密
// 3. 或者使用小程序原生的加密API（如果有的话）
// 4. 也可以考虑使用服务端加密，客户端只负责传输
