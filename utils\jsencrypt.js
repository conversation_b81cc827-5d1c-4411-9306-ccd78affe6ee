/**
 * RSA加密工具 - 小程序版本
 * 注意：小程序环境下的RSA加密实现
 */

// RSA公钥 - 应该从配置文件或环境变量中获取
const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7bT6uomAhiydpgPiZWqM
YrWVlFtXTzRqjjlVV7jF8YgqFJ5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5J
d5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5J
d5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5J
d5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5J
QIDAQAB
-----END PUBLIC KEY-----`

// RSA私钥 - 前端不建议存放私钥
const privateKey = `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDttPq6iYCGLJ2m
A+JlaoxitZWUW1dPNGqOOVVXuMXxiCoUnkl3kl3kl3kl3kl3kl3kl3kl3kl3kl3k
l3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3k
l3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3k
l3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3kl3k
wIDAQABAoIBAH5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5Jd5J
-----END PRIVATE KEY-----`

/**
 * 简单的RSA加密实现（小程序环境下的模拟版本）
 * 注意：这是一个简化版本，实际项目中需要使用真正的RSA加密库
 * @param {string} txt 要加密的文本
 * @returns {string} 加密后的文本
 */
export const encrypt = (txt) => {
  try {
    // 在小程序环境下，我们使用Base64编码作为简单的"加密"
    // 实际项目中应该使用真正的RSA加密库，如 node-rsa 的小程序版本
    const encoded = uni.arrayBufferToBase64(new TextEncoder().encode(txt).buffer)
    return encoded
  } catch (error) {
    console.error('RSA encrypt error:', error)
    return txt
  }
}

/**
 * 简单的RSA解密实现（小程序环境下的模拟版本）
 * @param {string} txt 要解密的文本
 * @returns {string} 解密后的文本
 */
export const decrypt = (txt) => {
  try {
    // 对应上面的"加密"，这里进行Base64解码
    const buffer = uni.base64ToArrayBuffer(txt)
    const decoded = new TextDecoder().decode(buffer)
    return decoded
  } catch (error) {
    console.error('RSA decrypt error:', error)
    return txt
  }
}

/**
 * 获取公钥
 * @returns {string}
 */
export const getPublicKey = () => {
  return publicKey
}

/**
 * 获取私钥
 * @returns {string}
 */
export const getPrivateKey = () => {
  return privateKey
}

// 注意：在实际项目中，建议使用以下方案：
// 1. 使用 crypto-js 的小程序版本进行AES加密
// 2. 使用 node-rsa 的小程序兼容版本进行RSA加密
// 3. 或者使用小程序原生的加密API（如果有的话）
// 4. 也可以考虑使用服务端加密，客户端只负责传输
