@font-face {
  font-family: 'iconfont'; /* Project id 4991172 */
  src:
    url('@/static/iconfont/iconfont.woff2?t=1755851221804') format('woff2'),
    url('@/static/iconfont/iconfont.woff?t=1755851221804') format('woff'),
    url('@/static/iconfont/iconfont.ttf?t=1755851221804') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-left:before {
  content: '\e648';
}

.icon-xiala:before {
  content: '\e61f';
}

.icon-fanhui-:before {
  content: '\e69c';
}

.icon-fanhui:before {
  content: '\e62b';
}
