# 小程序登录功能说明

## 功能概述

为小程序端创建了一个完整的登录系统，完全参考web端的登录逻辑，包括数据加密、token处理等安全机制，并集中管理了登录相关的依赖文件。

## 文件结构

```
├── pages/login/login.vue          # 登录页面
├── api/login.js                   # 登录相关API（支持加密）
├── api/network.js                 # 网络请求（已更新，支持加密和token）
├── store/user.js                  # 用户状态管理（已更新）
├── utils/auth.js                  # 认证工具函数
├── utils/crypto.js                # 加密工具函数（小程序版本）
├── utils/jsencrypt.js             # RSA加密工具（小程序版本）
├── utils/router-guard.js          # 路由守卫
└── api/config.js                  # API配置（已更新）
```

## 登录参数

小程序登录使用以下参数：

```javascript
const loginForm = {
  clientId: '428a8310cd442757ae699df5d894f051',
  grantType: 'xcx',
  tenantId: '000000',
  username: 'admin',
  password: 'admin123',
  rememberMe: false,
  code: 'xcxCode',
  uuid: ''
}
```

## 主要功能

### 1. 登录页面 (`pages/login/login.vue`)
- 简洁的UI设计，使用wot-design-uni组件
- 支持用户名密码登录
- 支持验证码功能
- 支持记住密码功能
- 响应式设计，适配不同屏幕尺寸

### 2. API管理 (`api/login.js`)
- `login(data)` - 小程序登录（**支持数据加密**）
- `getCodeImg()` - 获取验证码
- `getTenantList(isToken)` - 获取租户列表
- `wxLogin(data)` - 微信登录（预留）

### 3. 网络请求 (`api/network.js`)
- **完整的数据加密/解密机制**
- **自动token处理和刷新**
- **统一的错误处理**
- **防重复提交机制**
- 支持AES+RSA混合加密
- 自动添加Authorization和Clientid头

### 4. 用户状态管理 (`store/user.js`)
- 添加了`login(userInfo)`方法
- 统一的登录状态管理
- 自动保存登录信息到本地存储
- 正确的clientId配置

### 5. 加密工具 (`utils/crypto.js` & `utils/jsencrypt.js`)
- **AES加密/解密**（小程序兼容版本）
- **RSA加密/解密**（小程序兼容版本）
- **Base64编码/解码**
- **随机密钥生成**
- 完全参考web端的加密逻辑

### 6. 认证工具 (`utils/auth.js`)
- `isLoggedIn()` - 检查登录状态
- `redirectToLogin()` - 跳转到登录页
- `clearLoginInfo()` - 清除登录信息
- `getToken()` / `setToken()` - token管理

### 7. 路由守卫 (`utils/router-guard.js`)
- 页面访问权限控制
- 自动重定向未登录用户到登录页

## 使用方法

### 1. 启动应用
应用启动时会自动检查登录状态，未登录用户会被重定向到登录页。

### 2. 登录流程
1. 用户输入用户名和密码
2. 如果启用验证码，需要输入验证码
3. 点击登录按钮
4. 登录成功后跳转到首页（名片列表）

### 3. 在其他页面中使用
```javascript
import { useUserStore } from '@/store/user'
import { isLoggedIn, redirectToLogin } from '@/utils/auth'

// 检查登录状态
if (!isLoggedIn()) {
  redirectToLogin()
  return
}

// 使用用户store
const userStore = useUserStore()
const token = userStore.accessToken
```

## 网络请求集成

网络请求已经集成了认证功能：
- 自动添加Authorization头
- token过期自动跳转登录页
- 统一的错误处理

## 注意事项

1. 登录页面使用了wot-design-uni组件库，确保已正确安装
2. 登录成功后的跳转路径可以根据需要修改
3. 验证码功能可以通过`captchaEnabled`变量控制开关
4. 记住密码功能会将用户信息保存在本地存储中

## 自定义配置

### 修改登录参数
在`pages/login/login.vue`中修改`loginForm`的默认值。

### 修改API地址
在`api/config.js`中修改相关API路径。

### 修改跳转逻辑
在`utils/auth.js`中修改`redirectToLogin`函数。
