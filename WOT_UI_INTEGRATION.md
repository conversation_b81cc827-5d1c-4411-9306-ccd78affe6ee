# Wot-UI 集成说明

本项目已成功集成 Wot-UI (wot-design-uni) 组件库。

## 已完成的集成步骤

### 1. 安装依赖
```bash
pnpm add wot-design-uni
```

### 2. 配置 easycom 自动引入
由于使用了 npm 安装方式，wot-design-uni 组件通过 easycom 自动引入，无需在 main.js 中全局注册。

**重要说明**: 初始尝试在 main.js 中全局注册 WotDesign 时出现了导入错误，这是因为 wot-design-uni 推荐使用 easycom 方式自动引入组件，而不是全局注册。

### 3. 在 pages.json 中配置 easycom 自动引入
```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue"
    }
  }
}
```

## 已集成的组件示例

### 在首页 (pages/index/index.vue) 中使用的组件：
- **wd-search**: 搜索框组件
- **wd-tabs**: 标签页组件
- **wd-tab**: 标签页项
- **wd-tag**: 标签组件
- **wd-fab**: 浮动按钮
- **wd-button**: 按钮组件

### 专门的组件展示页面 (pages/wotDemo/wotDemo.vue)：
- **wd-navbar**: 导航栏
- **wd-cell-group**: 单元格组
- **wd-cell**: 单元格
- **wd-button**: 各种类型的按钮
- **wd-tag**: 各种类型的标签
- **wd-input**: 输入框
- **wd-textarea**: 多行文本输入
- **wd-picker**: 选择器
- **wd-steps**: 步骤条
- **wd-step**: 步骤项
- **wd-progress**: 进度条

## 使用方法

### 直接在模板中使用组件
由于配置了 easycom 自动引入，可以直接在 Vue 模板中使用 wot-ui 组件，无需手动引入：

```vue
<template>
  <wd-button type="primary">主要按钮</wd-button>
  <wd-search v-model="searchValue" placeholder="搜索内容" />
  <wd-tag type="success">成功标签</wd-tag>
</template>
```

### 查看组件展示
在首页点击"查看 Wot-UI 组件展示"按钮，可以跳转到专门的组件展示页面，查看各种组件的使用效果。

## 功能特性

1. **搜索功能**: 在首页可以搜索名片内容
2. **标签页切换**: 支持全部、最近、收藏三个标签页
3. **组件展示**: 专门的页面展示各种 wot-ui 组件
4. **响应式设计**: 组件适配小程序环境

## 注意事项

1. 所有组件都使用 `wd-` 前缀
2. 组件已配置自动引入，无需手动 import
3. 样式已适配项目的设计风格
4. 支持 Vue 3 Composition API

## 下一步扩展

可以根据项目需求继续集成更多 wot-ui 组件：
- 表单组件 (wd-form, wd-field)
- 数据展示组件 (wd-table, wd-list)
- 导航组件 (wd-tabbar, wd-sidebar)
- 反馈组件 (wd-dialog, wd-toast)
- 其他业务组件

更多组件使用方法请参考 [Wot Design Uni 官方文档](https://wot-design-uni.cn/)。

## 集成状态

✅ **编译成功**: 项目已成功编译，wot-ui 组件可以正常使用
✅ **自动引入**: easycom 配置正确，组件可以直接在模板中使用
✅ **示例完整**: 首页和组件展示页面都包含了丰富的 wot-ui 组件示例
✅ **代码格式化**: 所有代码已通过 prettier 格式化
