<template>
  <view class="login-container">
    <!-- 状态栏占位 -->
    <view :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 头部 -->
    <view class="header">
      <view class="title">EaseCard</view>
      <view class="subtitle">智能名片管理系统</view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <view class="form-item">
        <wd-input
          v-model="loginForm.username"
          placeholder="请输入用户名"
          prefix-icon="user"
          clearable
        />
      </view>
      
      <view class="form-item">
        <wd-input
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          prefix-icon="lock"
          clearable
          show-password
        />
      </view>

      <!-- 验证码 -->
      <view class="form-item" v-if="captchaEnabled">
        <view class="code-row">
          <wd-input
            v-model="loginForm.code"
            placeholder="请输入验证码"
            prefix-icon="shield"
            clearable
            class="code-input"
          />
          <view class="code-img" @click="getCode">
            <image v-if="codeUrl" :src="codeUrl" mode="aspectFit" />
            <text v-else>点击获取</text>
          </view>
        </view>
      </view>

      <!-- 记住密码 -->
      <view class="form-item">
        <wd-checkbox v-model="loginForm.rememberMe">记住密码</wd-checkbox>
      </view>

      <!-- 登录按钮 -->
      <view class="form-item">
        <wd-button
          type="primary"
          size="large"
          :loading="loading"
          @click="handleLogin"
          block
        >
          {{ loading ? '登录中...' : '登录' }}
        </wd-button>
      </view>
    </view>

    <!-- 底部 -->
    <view class="footer">
      <text>Copyright © 2018-2025 EaseCard All Rights Reserved.</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { getCodeImg } from '@/api/login'

const userStore = useUserStore()

// 状态栏高度
const statusBarHeight = ref(0)

// 登录表单数据
const loginForm = ref({
  clientId: '428a8310cd442757ae699df5d894f051',
  grantType: 'xcx',
  tenantId: '000000',
  username: 'admin',
  password: 'admin123',
  rememberMe: false,
  code: 'xcxCode',
  uuid: ''
})

// 页面状态
const loading = ref(false)
const captchaEnabled = ref(true)
const codeUrl = ref('')

/**
 * 获取验证码
 */
const getCode = async () => {
  try {
    const res = await getCodeImg()
    if (res.code === 200) {
      const { data } = res
      captchaEnabled.value = data.captchaEnabled !== false
      if (captchaEnabled.value) {
        codeUrl.value = 'data:image/gif;base64,' + data.img
        loginForm.value.uuid = data.uuid
      }
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

/**
 * 处理登录
 */
const handleLogin = async () => {
  // 简单验证
  if (!loginForm.value.username) {
    uni.showToast({
      title: '请输入用户名',
      icon: 'none'
    })
    return
  }
  
  if (!loginForm.value.password) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    })
    return
  }

  if (captchaEnabled.value && !loginForm.value.code) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none'
    })
    return
  }

  loading.value = true

  try {
    // 记住密码处理
    if (loginForm.value.rememberMe) {
      uni.setStorageSync('loginForm', {
        username: loginForm.value.username,
        password: loginForm.value.password,
        rememberMe: true
      })
    } else {
      uni.removeStorageSync('loginForm')
    }

    // 调用登录接口
    await userStore.login(loginForm.value)
    
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })

    // 登录成功后跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/cardList/cardList'
      })
    }, 1500)

  } catch (error) {
    console.error('登录失败:', error)
    // 重新获取验证码
    if (captchaEnabled.value) {
      await getCode()
    }
  } finally {
    loading.value = false
  }
}

/**
 * 获取记住的登录信息
 */
const getRememberedLogin = () => {
  const remembered = uni.getStorageSync('loginForm')
  if (remembered && remembered.rememberMe) {
    loginForm.value.username = remembered.username
    loginForm.value.password = remembered.password
    loginForm.value.rememberMe = remembered.rememberMe
  }
}

onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 获取验证码
  getCode()
  
  // 获取记住的登录信息
  getRememberedLogin()
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  padding: 80rpx 0 60rpx;
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-form {
  flex: 1;
  padding: 60rpx 60rpx 0;
  background: #fff;
  border-radius: 40rpx 40rpx 0 0;
  
  .form-item {
    margin-bottom: 40rpx;
  }
  
  .code-row {
    display: flex;
    align-items: center;
    gap: 20rpx;
    
    .code-input {
      flex: 1;
    }
    
    .code-img {
      width: 200rpx;
      height: 80rpx;
      border: 2rpx solid #e4e7ed;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f7fa;
      
      image {
        width: 100%;
        height: 100%;
        border-radius: 6rpx;
      }
      
      text {
        font-size: 24rpx;
        color: #909399;
      }
    }
  }
}

.footer {
  padding: 40rpx;
  text-align: center;
  
  text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}
</style>
