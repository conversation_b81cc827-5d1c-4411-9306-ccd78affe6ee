<template>
  <view class="login-container">
    <!-- 状态栏占位 -->
    <view :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 头部 -->
    <view class="header">
      <view class="title">EaseCard</view>
      <view class="subtitle">智能名片管理系统</view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 显示登录参数信息 -->
      <view class="login-info">
        <view class="info-item">
          <text class="label">客户端ID:</text>
          <text class="value">{{ loginForm.clientId }}</text>
        </view>
        <view class="info-item">
          <text class="label">授权类型:</text>
          <text class="value">{{ loginForm.grantType }}</text>
        </view>
        <view class="info-item">
          <text class="label">租户ID:</text>
          <text class="value">{{ loginForm.tenantId }}</text>
        </view>
        <view class="info-item">
          <text class="label">小程序Code:</text>
          <text class="value">{{ loginForm.xcxCode || '待获取' }}</text>
        </view>
      </view>

      <!-- 获取Code按钮 -->
      <view class="form-item">
        <wd-button
          type="info"
          size="large"
          @click="getXcxCode"
          block
        >
          获取小程序Code
        </wd-button>
      </view>

      <!-- 登录按钮 -->
      <view class="form-item">
        <wd-button
          type="primary"
          size="large"
          :loading="loading"
          :disabled="!loginForm.xcxCode"
          @click="handleLogin"
          block
        >
          {{ loading ? '登录中...' : '登录' }}
        </wd-button>
      </view>
    </view>

    <!-- 底部 -->
    <view class="footer">
      <text>Copyright © 2018-2025 EaseCard All Rights Reserved.</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// 状态栏高度
const statusBarHeight = ref(0)

// 登录表单数据
// resolved 登录参数要求为以下四个：clientId、grantType、tenantId、xcxCode（传递小程序获取的code），其余不需要，点击登录时console这四个参数便于调试
const loginForm = ref({
  clientId: '428a8310cd442757ae699df5d894f051',
  grantType: 'xcx',
  tenantId: '779984',
  xcxCode: '' // 小程序获取的code
})

// 页面状态
const loading = ref(false)

/**
 * 获取小程序code
 */
const getXcxCode = () => {
  uni.login({
    provider: 'weixin',
    success: (res) => {
      console.log('获取小程序code成功:', res.code)
      loginForm.value.xcxCode = res.code
      uni.showToast({
        title: 'Code获取成功',
        icon: 'success'
      })
    },
    fail: (error) => {
      console.error('获取小程序code失败:', error)
      uni.showToast({
        title: 'Code获取失败',
        icon: 'none'
      })
    }
  })
}

/**
 * 处理登录
 */
const handleLogin = async () => {
  // 验证是否已获取小程序code
  if (!loginForm.value.xcxCode) {
    uni.showToast({
      title: '请先获取小程序Code',
      icon: 'none'
    })
    return
  }

  // Console输出登录参数用于调试
  console.log('=== 登录参数 ===')
  console.log('clientId:', loginForm.value.clientId)
  console.log('grantType:', loginForm.value.grantType)
  console.log('tenantId:', loginForm.value.tenantId)
  console.log('xcxCode:', loginForm.value.xcxCode)
  console.log('===============')

  loading.value = true

  try {
    // 调用登录接口
    await userStore.login(loginForm.value)

    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })

    // 登录成功后跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/cardList/cardList'
      })
    }, 1500)

  } catch (error) {
    console.error('登录失败:', error)
    // 重新获取小程序code
    loginForm.value.xcxCode = ''
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0

  // 自动获取小程序code
  getXcxCode()
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  padding: 80rpx 0 60rpx;
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-form {
  flex: 1;
  padding: 60rpx 60rpx 0;
  background: #fff;
  border-radius: 40rpx 40rpx 0 0;

  .login-info {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }

      .value {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
        max-width: 300rpx;
        text-align: right;
        word-break: break-all;
      }
    }
  }

  .form-item {
    margin-bottom: 40rpx;
  }
}

.footer {
  padding: 40rpx;
  text-align: center;
  
  text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}
</style>
