import { sys, apiMap } from './config'
import { redirectToLogin, getToken } from '@/utils/auth'
const _request = function (options) {
  const access_token = getToken()
  options.timeout = sys.timeout
  options.header = {
    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
    'Cache-Control': 'no-cache',
  }
  if (options.isJson) {
    options.header['Content-Type'] = 'application/json'
  }
  if (options.isFormData) {
    options.header['Content-Type'] = 'multipart/form-data'
  }
  if (!options.noAuth) {
    // 默认需要权限，token 无效则需要跳转到登录页
    if (!access_token) {
      uni.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000,
      })
      setTimeout(() => {
        redirectToLogin()
      }, 2000)
      return Promise.reject({ message: '未登录' })
    }
    options.header.Authorization = 'Bearer ' + access_token
    const loginRes = uni.getStorageSync('loginRes') || {}
    options.header.Clientid = loginRes?.client_id
  }

  return new Promise((resolve, reject) => {
    const task = uni.request({
      ...options,
      success(res) {
        if (res.statusCode !== 200) {
          // 接口请求出错
          console.log('request-success-error:', res)
          uni.showToast({
            icon: 'none',
            title: `请求出错`,
            duration: 2000,
          })
        }
        // 需要源数据返回
        if (options.native) {
          return resolve(res)
        }
        // token 过期
        if (res.data.code == 401) {
          uni.showToast({
            icon: 'none',
            title: `token过期，请重新登录`,
            duration: 2000,
          })
          // 清除登录信息
          uni.removeStorageSync('loginRes')
          uni.removeStorageSync('loginForm')
          // 跳转到登录页
          setTimeout(() => {
            redirectToLogin()
          }, 2000)
        }
        // 返回数据
        return resolve(res.data)
      },
      fail(e) {
        console.log('request-fail', e)
      },
      complete: () => {},
    })
  })
}

/**
 *
 * @param {*} key  地址
 * @param {*} params  请求参数
 * @param {*} method  请求方法， 权限
 */
const request = function (key, data = {}, method, options = {}) {
  let ifRight = key.indexOf('.')
  let url = ''
  if (ifRight != -1) {
    const keys = key.split('.')
    url = keys.reduce((sum, k) => {
      if (k) {
        sum = sum[k]
      }
      return sum
    }, apiMap)
  } else {
    url = key
  }
  url = url.includes('http') ? url : sys.host + url
  options.url = options.urlpj ? sys.host + options.url : url
  options.data = data
  options.method = method || 'GET'
  return _request(options)
}

const get = (url, data = {}, options = {}) => {
  return request(url, data, 'GET', options)
}
const post = (url, data = {}, options = {}) => {
  return request(url, data, 'POST', options)
}

export default {
  request,
  get,
  post,
}
