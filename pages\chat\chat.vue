<template>
  <view class="height-100vh box-border bg-light page-wrap flex flex-column overflow-y-hidden">
    <view class="flex-1 position-relative" id="scrollViewWrap">
      <scroll-view
        ref="scrollView"
        id="scrollView"
        scroll-y="true"
        class="list-wrap box-border no-scrollbar"
        :style="{
          height: isStop ? `${scrollViewHeight - 30}px` : `${scrollViewHeight}px`,
        }"
        :refresher-enabled="true"
        :scroll-with-animation="false"
        :show-scrollbar="false"
        :scroll-anchoring="true"
        :scroll-top="scrollTop"
        :refresher-triggered="refresherTriggered"
        @refresherpulling="onRefresherpulling"
        @refresherrefresh="onScrolltoupper"
        @refresherrestor="onRefresherrestor"
        @refresherabort="onRefresherabort"
        @scroll="onScroll"
      >
        <view class="px-4">
          <!-- 名片 -->
          <view class="card-wrap mb-4">
            <view
              class="card-main position-relative rounded-12 p-base box-border bg-light-detail flex align-center"
            >
              <view class="logo-box">
                <image
                  src="/static/images/<EMAIL>"
                  mode="aspectFit"
                  class="width-100 height-100"
                ></image>
              </view>
              <view class="flex-1 text-theme-brand pt-2">
                <view class="font-weight-bold font-14 mb-1">您好，我是{{ detail.name }}</view>
                <view class="font-12">很高兴为您服务，有什么问题尽管问</view>
              </view>
              <view class="flex-shrink flex justify-end">
                <button
                  open-type="share"
                  class="bg-theme-brand text-white py-1 font-12 flex align-center justify-evenly rounded-8 card-box"
                >
                  <view class="share-box flex algin-center justify-center">
                    <image
                      src="/static/images/<EMAIL>"
                      mode="aspectFit"
                      class="width-100 height-100"
                    ></image>
                  </view>
                  名片
                </button>
              </view>
            </view>
          </view>
          <!--列表 -->
          <view class="flex flex-row flex-wrap pb-base">
            <template v-for="(item, inex) in chatList">
              <view v-if="item.role == 'user'" class="flex justify-end width-100 list-item-hook">
                <view
                  class="mb-4 bg-theme-brand text-white font-14 rounded-8 rounded-top-right-0 d-inline-block py-2 px-1-2"
                >
                  {{ item.content }}
                </view>
              </view>
              <template v-else>
                <view v-if="item.role == 'loading'" class="gif-box mb-4">
                  <image
                    src="/static/images/loading.gif"
                    mode="aspectFit"
                    class="width-100 height-100"
                  ></image>
                </view>
                <view v-else class="flex width-100 list-item-hook">
                  <view
                    v-if="item.content"
                    class="mb-4 bg-white text-theme-brand font-14 rounded-8 rounded-top-left-0 d-inline-block"
                  >
                    <zero-markdown-view :markdown="item.content"></zero-markdown-view>
                  </view>
                </view>
              </template>
            </template>
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 底部按钮 -->
    <view
      class="flex-shrink pageFoot box-border p-4 pt-0"
      :style="{ marginBottom: systemInfo.safeAreaInsets.bottom + 'px' }"
    >
      <view v-if="isStop" class="flex justify-center text-second font-12 stop-wrap">
        你中止了本次回答
        <text class="mx-0-8" style="color: #4d5bec" @click.stop="onReAsk">重新编辑问题</text>
      </view>
      <view class="scroll-row mb-base box-border overflow-x-auto no-scrollbar">
        <view
          v-for="(item, index) in detail.skills"
          :key="index"
          class="rounded-80 py-1 px-3 text-second bg-info font-12 mr-base mb scroll-row-item"
          @tap="onSelectPrequestion(item)"
          :class="isReply ? 'opacity-0_5' : ''"
        >
          {{ item.content }}
        </view>
      </view>
      <view class="flex align-center justify-evenly mb-base">
        <view
          class="flex-shrink btn-box rounded-circle bg-white p-1 flex align-center justify-center"
        >
          <image
            src="/static/images/<EMAIL>"
            mode="aspectFit"
            class="width-100 height-100"
          ></image>
        </view>
        <view class="flex-1 px-base position-relative">
          <input
            type="text"
            :disabled="isReply"
            :cursor-spacing="20"
            v-model="userMessage"
            placeholder="请输入您的问题"
            class="input px-3"
            :class="isReply ? 'disabled' : ''"
            placeholder-class="text-placeholder font-14"
            @confirm="onSend"
          />
        </view>
        <template>
          <view
            v-if="!isReply"
            class="flex-shrink btn-box rounded-circle bg-white p-1 flex align-center justify-center"
            hover-class="bg-hover"
            @tap="onSend"
          >
            <image
              src="/static/images/<EMAIL>"
              mode="aspectFit"
              class="width-100 height-100"
            ></image>
          </view>
          <view
            v-else
            class="flex-shrink btn-box rounded-circle bg-white p-1 flex align-center justify-center"
            hover-class="bg-hover"
            @tap.stop="onStop"
          >
            <image
              src="/static/images/<EMAIL>"
              mode="aspectFit"
              class="width-100 height-100"
            ></image>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
// import zeroMarkdownView from '/uni_modules/zero-markdown-view/components/zero-markdown-view/zero-markdown-view.vue';
import { ref, nextTick, watch, onMounted, onUnmounted, reactive } from 'vue'
import { onShareAppMessage, onLoad, onReady, onUnload } from '@dcloudio/uni-app'
import { apiMap, $R } from '@/api/config'
import { useUserStore } from '@/store/user'
import { sys } from '@/api/config.js'
import mockJson from '@/api/mock-card.json'

const PAGE_SIZE = 10

// 变量
const userStore = useUserStore()
const socketTask = ref(null)
const scrollView = ref(null)
const cardId = ref('')
const detail = ref({
  skills: [],
}) // 名片信息
const activePrequestionIndex = ref(-1)
const agentInfo = ref({}) // 租户信息
const nowSocketId = ref(0)
const isReply = ref(false) // 是否正在回复中
const isStop = ref(false) // 中止会话
const socketReceiveNum = ref(0)
const connectOk = ref(false) // socket 是否链接成功
const scrollTop = ref(0) // 滚动条位置
const scrollType = ref('lower') // 滚动的类型 : lower，滚到最底部;none：不做滚动
const scrollViewHeight = ref(400) // 滚动区域高度
const userMessage = ref('') // 问题
const systemInfo = reactive({
  osName: '',
  safeAreaInsets: {},
}) // 系统信息
const chatList = ref([]) // 聊天列表
const refresherTriggered = ref(false) // 下拉刷新
const pageNum = ref(1) // 分页
const total = ref(0) // 总条数
const loading = ref(false) // 聊天记录加载中

// watch(   //
// 	[()=>chatList.value.length,
// 	socketReceiveNum
// 	],
// 	(e) => {
// 		const len=chatList.value.length
// 		if(e[1]!=0 || chatList.value[len-1].content==''){
// 			isReply.value=true
// 		}else{
// 			isReply.value=false
// 		}
// 	}
// );
watch([connectOk, agentInfo], (e) => {
  if (e[0] && e[1].id) {
    if (activePrequestionIndex.value != -1) {
      const q = detail.value.skills[activePrequestionIndex.value].content
      _completions(q)
    }
  }
})
/*生命周期*/
onReady(() => {
  const scrollViewWrap = uni.createSelectorQuery().select('#scrollViewWrap')
  scrollViewWrap
    .boundingClientRect((res) => {
      if (res) {
        scrollViewHeight.value = res.height
      }
    })
    .exec()
})
onLoad((e) => {
  if (e.id) {
    cardId.value = e.id
    detail.value = mockJson[cardId.value]
    if (e.question) {
      const question = JSON.parse(e.question)
      const index = detail.value.skills.findIndex((item) => item.id == question.id)
      activePrequestionIndex.value = index
      chatList.value.push(question)
    }
  }
  uni.getSystemInfo({
    success: function (res) {
      systemInfo.osName = res.osName
      systemInfo.safeAreaInsets = res.safeAreaInsets
    },
  })
  initPage()
})
onUnload(() => {
  onStop('正在退出对话')
  socketTask.value.close()
})
onUnmounted(() => {
  socketTask.value.close()
})

//用户点击分享
onShareAppMessage((e) => {
  let shareInfo = {
    path: `/pages/cardDetail/cardDetail?id=${detail.value.id}`,
    title: `${detail.value.name} - ${detail.value.company}｜${detail.value.job}`,
    imageUrl: detail.value.sharePic,
  }
  return shareInfo
})
/*函数*/
/*
只有onScroll 事件才能拿到最真实的scrollHeight。所以最终的滚动都在这里完成，其他地方改变scrollTop，只是为了触发onScroll 事件
*/
function onScroll(e) {
  // console.log("onScorll",e,scrollType.value)
  const near = 300 // 自定义一个区间
  const detail = e.detail
  const h = Math.ceil(detail.scrollHeight)
  switch (scrollType.value) {
    case 'lower':
      // if(scrollTop.value!=h){
      // 	scrollTop.value=h
      // }
      if (socketReceiveNum.value == 0) {
        scrollType.value = ''
        if (scrollTop.value != h) {
          scrollTop.value = h
        }
      } else {
        const a = Math.abs(scrollTop.value - h)
        if (a > 200) {
          scrollTop.value = h
        }
      }
      break
    case 'none':
      // scrollTop.value=detail.scrollTop  // 赋值页面会闪
      break
    default:
  }
}
// 自定义下拉刷新（这里是查看历史记录）
function onRefresherpulling() {
  refresherTriggered.value = true
}
function onRefresherrestor() {
  refresherTriggered.value = false
}
function onRefresherabort() {
  refresherTriggered.value = false
}
//滑动到顶部
function onScrolltoupper() {
  if (loading.value) {
    return
  }
  const pages = Math.ceil(total.value / PAGE_SIZE)
  scrollType.value = 'none'
  if (pageNum.value < pages) {
    pageNum.value++
    _record((data = []) => {
      const d = data.slice().reverse()
      chatList.value = [...d, ...chatList.value]
    })
  } else {
    loading.value = false
    refresherTriggered.value = false
  }
}
// 初始化页面
async function initPage() {
  socketTask.value = uni.connectSocket({
    url: `wss://${sys.ip}/${apiMap.conversation.websocket}`,
    header: {
      Authorization: `Bearer ${userStore.accessToken}`,
      Clientid: userStore?.loginRes?.client_id,
    },
    success: (res) => {
      connectOk.value = true
    },
    fail: (err) => {
      connectOk.value = false
      console.log('connectSocket：err', err)
      uni.showToast({
        title: `网络异常：${err}`,
        icon: 'none',
        duration: 2000,
      })
    },
    complete: () => {},
  })
  socketTask.value.onOpen((e) => {
    console.log('socket:打开成功')
    /*测试*/
    // setInterval(()=>{
    // 	socketTask.value.send({
    // 		data:"ping" ,
    // 		success: () => {
    // 			console.log('心跳包发送成功');
    // 		  },
    // 		  fail: (err) => {
    // 			console.error('心跳包发送失败:', err);
    // 		  }
    // 	})
    // },30000)
    /*测试*/
    socketTask.value.onMessage((e) => {
      const d = JSON.parse(e.data)
      const data = { ...d, role: 'assistant' }
      const clen = chatList.value.length
      // let lastChatItem=clen >0  ? chatList.value[clen-1] : {}
      const index = chatList.value.findIndex((item, index) => {
        return item.id == data.id
      })
      nowSocketId.value = data.id
      // console.log("index",index)
      // const i=chatList.value.lengt
      if (!data.end) {
        socketReceiveNum.value++
        if (!isStop.value) {
          isReply.value = true
        }
        if (index < 0) {
          // console.log("覆盖1")
          chatList.value[clen - 1] = { ...data, role: 'loading' } // 覆盖空数据
          // console.log("覆盖",chatList.value[clen-1])
        } else {
          if (data.content) {
            // console.log("++")
            const c = chatList.value[clen - 1].content + data.content
            chatList.value[clen - 1]['content'] = c
            chatList.value[clen - 1].role = data.role
            // chatList.value[clen-1].content+=data.content
          }
          // console.log("chatlist-content",chatList.value[i-1].content)
        }
      } else {
        if (index < 0) {
          chatList.value[clen - 1] = data
        }
        chatList.value[clen - 1].role = data.role
        socketReceiveNum.value = 0
        isReply.value = false
      }
      nextTick(() => {
        _scrollToBottom('add')
      })
    })
  })
  socketTask.value.onClose((e) => {
    socketReceiveNum.value = 0
    isReply.value = false
    console.log('socket:关闭成功')
  })
  socketTask.value.onError((e) => {
    socketReceiveNum.value = 0
    isReply.value = false
    console.log('socket:链接出错', e.errMsg)
  })

  await _getOrCreateByAgent()
  _record((data = []) => {
    const d = data.slice().reverse()
    if (chatList.value.length > 0) {
      chatList.value = [...d, ...chatList.value]
    } else {
      chatList.value = d
    }
    nextTick(() => {
      _scrollToBottom('record')
    })
  })
}
// 发送对话
async function _completions(question = '') {
  const msg = userMessage.value
  userMessage.value = ''
  _chatloading()
  const reqdata = {
    conversationId: agentInfo.value.id,
    userMessage: question || msg,
    hasImages: false,
  }
  const res = await $R.post(apiMap.conversation.completions, reqdata, { isJson: true })
  if (res.code === 200) {
    userMessage.value = ''
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 2000,
    })
  }
}

// 查询记录
async function _record(callback = () => {}) {
  loading.value = true
  const reqdata = {
    conversationId: agentInfo.value.id,
    pageNum: pageNum.value,
    pageSize: PAGE_SIZE,
    orderByColumn: 'id',
    isAsc: 'desc',
  }
  const res = await $R.get(apiMap.conversation.record, reqdata, { isJson: true })
  loading.value = false
  refresherTriggered.value = false
  if (res.code === 200) {
    total.value = res.total
    if (typeof callback === 'function') {
      callback(res.rows)
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 2000,
    })
  }
  return res
}
// 创建会话
async function _getOrCreateByAgent() {
  uni.showLoading({
    mask: true,
  })
  const reqdata = {
    agentId: detail.value.agentId,
    outwardType: 'WEB',
  }
  const res = await $R.get(apiMap.conversation.getOrCreateByAgent, reqdata, { isJson: true })
  uni.hideLoading()
  if (res.code === 200) {
    agentInfo.value = res.data
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 2000,
    })
  }
  return res
}
function onRichTextClick() {}
//滑倒最底部
function _scrollToBottom(type = 'record') {
  scrollType.value = 'lower'
  const query = uni.createSelectorQuery()
  query.selectAll('#scrollView').boundingClientRect()
  query.selectAll('.list-item-hook').boundingClientRect()
  query.exec((res) => {
    const scrollView = res[0][0]
    const items = res[1]
    if (type == 'record') {
      if (items && items.length > 0) {
        const len = items.length
        const lastItem = items[len - 1]
        scrollTop.value = lastItem.bottom
      }
    }
    if (type == 'add') {
      if (scrollTop.value >= scrollView.height) {
        scrollTop.value += 30
        // console.log("scrollTop1",scrollTop.value)
      }
    }
  })
}

//选择快捷问题
function onSelectPrequestion(item = {}) {
  if (!connectOk.value) return
  if (isReply.value) return
  chatList.value.push(item)
  isStop.value = false
  nextTick(() => {
    _scrollToBottom('add')
  })
  _completions(item.content)
}
// 发送问题
function onSend() {
  if (!connectOk.value) return
  if (isReply.value) return
  const _content = userMessage.value.trim()
  if (_content) {
    const obj = {
      content: _content,
      role: 'user',
    }
    chatList.value.push(obj)
    isStop.value = false
    nextTick(() => {
      _scrollToBottom('add')
    })
    _completions()
  }
}
// 终止 发送
async function onStop(title = '正在中止...') {
  const len = chatList.value.length
  // if(!chatList.value[len-1].content){
  // 	chatList.value.pop()
  // }
  uni.showLoading({
    mask: true,
    title: title,
  })
  const res = await $R.post(apiMap.conversation.close + '/' + nowSocketId.value)
  uni.hideLoading()
  if (res.code === 200) {
    isReply.value = false
    isStop.value = true
    // (用户已终止对话)
    if (chatList.value[len - 1].role == 'loading') {
      chatList.value[len - 1].content = '(用户已终止对话)'
      chatList.value[len - 1].role = 'assistant'
      nextTick(() => {
        _scrollToBottom('add')
      })
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 2000,
    })
  }
}
// 重新编辑
function onReAsk() {
  isReply.value = false
  isStop.value = false
}
/*
ai 时间长，先push 一个空数据进去
*/
function _chatloading() {
  const obj = {
    id: 0,
    content: '',
    role: 'loading',
  }
  chatList.value.push(obj)
  isReply.value = true
}
</script>

<style scoped lang="scss">
.gif-box {
  width: 60rpx;
  height: 60rpx;
}
.list-wrap {
  // &::-webkit-scrollbar {
  // 	display: none;
  // 	width: 0;
  // 	height: 0;
  // 	color: red;
  // 	background: transparent;
  // }
}
.card-wrap {
  padding-top: 80rpx;
}
.card-main {
  padding-top: 30rpx;
}
.share-box {
  $w: 32rpx;
  width: $w;
  height: $w;
}
.logo-box {
  $w: 112rpx;
  width: $w;
  height: $w;
  position: absolute;
  top: 0;
  transform: translateY(-60%);
  left: 20rpx;
  z-index: 5;
}
.card-box {
  width: 120rpx;
  box-sizing: border-box;
  padding: 0;
}
.btn-box {
  $w: 72rpx;
  width: $w;
  height: $w;
  box-sizing: border-box;
}
.input {
  $w: 88rpx;
  height: $w;
  border-radius: $w;
  background-color: #fff;
  position: relative;
  &.disabled {
    background-color: rgba(#fff, 0.5);
  }
}
.chat-right {
  border-top-right-radius: 0;
}
.chat-right {
  border-top-left-radius: 0;
}
.pageFoot {
  // height:118px;
}
.stop-wrap {
  height: 30px;
}
</style>
